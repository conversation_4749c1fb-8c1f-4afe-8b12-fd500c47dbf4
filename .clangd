CompileFlags:
  Add:
    - '--target=arm-none-eabi'
    - '-Ic:/Keil_v5/ARM/ARMCC/include'
    - '-Ic:/Keil_v5/ARM/ARMCC/include/rw'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/inc'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ble/controller/include'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/osal/include'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/common'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ble/include'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ble/hci'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ble/host'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/Profiles/DevInfo'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/Profiles/SimpleProfile'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/Profiles/Roles'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/common'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/clock'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/gpio'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/pwrmgr'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/log'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/uart'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/flash'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/pwm'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/example/ble_mesh/mesh_gateway/source'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/example/ble_mesh/mesh_gateway/source/bleMesh'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/external/crypto/aes'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/include'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/bearer'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/platforms/ext'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/osal/src/phyos'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/utils/include'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/appl'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/osal/snv'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/external/crypto/asm_ecdh_p256'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/lib'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/cbtimer'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/arch/cm0'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/misc'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ble/controller'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/libraries/crc16'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/timer'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/climodel'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/libraries/fs'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/libraries/cli'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/vendormodel'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/vendormodel/client'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/mesh/export/vendormodel/server'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/platforms'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/platforms/interfaces/crypto'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/ethermind/platforms/mesh'
    - '-Id:/BLE_SinglePhase_Electric_Meter/phy_ble/softwore/PHY6222_gateway/components/driver/led_light'
    - '-DCFG_CP'
    - '-DOSAL_CBTIMER_NUM_TASKS=1'
    - '-DMTU_SIZE=247'
    - '-DOSALMEM_METRICS=0'
    - '-DDEBUG_INFO=1'
    - '-DCFG_SLEEP_MODE=PWR_MODE_NO_SLEEP'
    - '-DCFG_HEARTBEAT_MODE=0'
    - '-DPHY_MCU_TYPE=MCU_BUMBEE_M0'
    - '-DUSE_FS=0'
    - '-DMAX_NUM_LL_CONN=1'
    - '-DGATT_MAX_NUM_CONN=MAX_NUM_LL_CONN+1'
    - '-DGATT_PVNR=0'
    - '-DMESH_HEAP=0'
    - '-D_RTE_'
    - '-D__CC_ARM'
    - '-D__arm__'
    - '-D__align(x)='
    - '-D__ALIGNOF__(x)='
    - '-D__alignof__(x)='
    - '-D__asm(x)='
    - '-D__forceinline='
    - '-D__restrict='
    - '-D__global_reg(n)='
    - '-D__inline='
    - '-D__int64=long long'
    - '-D__INTADDR__(expr)=0'
    - '-D__irq='
    - '-D__packed='
    - '-D__pure='
    - '-D__smc(n)='
    - '-D__svc(n)='
    - '-D__svc_indirect(n)='
    - '-D__svc_indirect_r7(n)='
    - '-D__value_in_regs='
    - '-D__weak='
    - '-D__writeonly='
    - '-D__declspec(x)='
    - '-D__attribute__(x)='
    - '-D__nonnull__(x)='
    - '-D__register='
    - '-D__breakpoint(x)='
    - '-D__cdp(x,y,z)='
    - '-D__clrex()='
    - '-D__clz(x)=0U'
    - '-D__current_pc()=0U'
    - '-D__current_sp()=0U'
    - '-D__disable_fiq()='
    - '-D__disable_irq()='
    - '-D__dmb(x)='
    - '-D__dsb(x)='
    - '-D__enable_fiq()='
    - '-D__enable_irq()='
    - '-D__fabs(x)=0.0'
    - '-D__fabsf(x)=0.0f'
    - '-D__force_loads()='
    - '-D__force_stores()='
    - '-D__isb(x)='
    - '-D__ldrex(x)=0U'
    - '-D__ldrexd(x)=0U'
    - '-D__ldrt(x)=0U'
    - '-D__memory_changed()='
    - '-D__nop()='
    - '-D__pld(...)='
    - '-D__pli(...)='
    - '-D__qadd(x,y)=0'
    - '-D__qdbl(x)=0'
    - '-D__qsub(x,y)=0'
    - '-D__rbit(x)=0U'
    - '-D__rev(x)=0U'
    - '-D__return_address()=0U'
    - '-D__ror(x,y)=0U'
    - '-D__schedule_barrier()='
    - '-D__semihost(x,y)=0'
    - '-D__sev()='
    - '-D__sqrt(x)=0.0'
    - '-D__sqrtf(x)=0.0f'
    - '-D__ssat(x,y)=0'
    - '-D__strex(x,y)=0U'
    - '-D__strexd(x,y)=0'
    - '-D__strt(x,y)='
    - '-D__swp(x,y)=0U'
    - '-D__usat(x,y)=0U'
    - '-D__wfe()='
    - '-D__wfi()='
    - '-D__yield()='
    - '-D__vfp_status(x,y)=0'
  Compiler: C:/Keil_v5/ARM/ARMCC/bin/armcc.exe


---
If:
  PathMatch: */CMSIS/.*
Diagnostics:
  Suppress: ["*", undeclared_var_use_suggest]