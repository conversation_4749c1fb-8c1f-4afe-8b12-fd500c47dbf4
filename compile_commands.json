[{"directory": "example/ble_mesh/mesh_gateway/source", "file": "example/ble_mesh/mesh_gateway/source/main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/main.o"]}, {"directory": "components/driver/pwm", "file": "components/driver/pwm/pwm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwm/pwm.c", "-o", "build/Target 1/components/driver/pwm/pwm.o"]}, {"directory": "components/driver/flash", "file": "components/driver/flash/flash.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/flash/flash.c", "-o", "build/Target 1/components/driver/flash/flash.o"]}, {"directory": "components/driver/clock", "file": "components/driver/clock/clock.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/clock/clock.c", "-o", "build/Target 1/components/driver/clock/clock.o"]}, {"directory": "components/driver/log", "file": "components/driver/log/my_printf.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/log/my_printf.c", "-o", "build/Target 1/components/driver/log/my_printf.o"]}, {"directory": "components/driver/gpio", "file": "components/driver/gpio/gpio.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/gpio/gpio.c", "-o", "build/Target 1/components/driver/gpio/gpio.o"]}, {"directory": "components/driver/uart", "file": "components/driver/uart/uart.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/uart/uart.c", "-o", "build/Target 1/components/driver/uart/uart.o"]}, {"directory": "components/driver/pwrmgr", "file": "components/driver/pwrmgr/pwrmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwrmgr/pwrmgr.c", "-o", "build/Target 1/components/driver/pwrmgr/pwrmgr.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.o"]}, {"directory": "components/libraries/crc16", "file": "components/libraries/crc16/crc16.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/crc16/crc16.c", "-o", "build/Target 1/components/libraries/crc16/crc16.o"]}, {"directory": "components/profiles/DevInfo", "file": "components/profiles/DevInfo/devinfoservice.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/DevInfo/devinfoservice.c", "-o", "build/Target 1/components/profiles/DevInfo/devinfoservice.o"]}, {"directory": "components/profiles/GATT", "file": "components/profiles/GATT/gattservapp.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/GATT/gattservapp.c", "-o", "build/Target 1/components/profiles/GATT/gattservapp.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/peripheral.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/peripheral.c", "-o", "build/Target 1/components/profiles/Roles/peripheral.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapgattserver.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapgattserver.c", "-o", "build/Target 1/components/profiles/Roles/gapgattserver.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gap.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gap.c", "-o", "build/Target 1/components/profiles/Roles/gap.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/observer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/observer.c", "-o", "build/Target 1/components/profiles/Roles/observer.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/central.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/central.c", "-o", "build/Target 1/components/profiles/Roles/central.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapbondmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapbondmgr.c", "-o", "build/Target 1/components/profiles/Roles/gapbondmgr.o"]}, {"directory": "misc", "file": "misc/jump_table.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "misc/jump_table.c", "-o", "build/Target 1/misc/jump_table.o"]}, {"directory": "components/osal/snv", "file": "components/osal/snv/osal_snv.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/osal/snv/osal_snv.c", "-o", "build/Target 1/components/osal/snv/osal_snv.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.o"]}, {"directory": "components/driver/led_light", "file": "components/driver/led_light/led_light.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/led_light/led_light.c", "-o", "build/Target 1/components/driver/led_light/led_light.o"]}, {"directory": "components/ethermind/platforms", "file": "components/ethermind/platforms/EM_platform.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/EM_platform.c", "-o", "build/Target 1/components/ethermind/platforms/EM_platform.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes-ccm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes-ccm.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes-ccm.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_debug.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_debug.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_debug.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_os.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_os.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_os.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_timer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_timer.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_timer.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/blebrr_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/blebrr_pl.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/blebrr_pl.o"]}, {"directory": "components/ethermind/platforms/interfaces/crypto", "file": "components/ethermind/platforms/interfaces/crypto/cry.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/interfaces/crypto/cry.c", "-o", "build/Target 1/components/ethermind/platforms/interfaces/crypto/cry.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr_gatt.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/MS_common_pl.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/prov_pl.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_services.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_services.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_services.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_prov.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_prov.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_prov.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_proxy.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_proxy.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_proxy.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_clients.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_clients.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_clients.o"]}, {"directory": "components/ethermind/mesh/export/cbtimer", "file": "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "-o", "build/Target 1/components/ethermind/mesh/export/cbtimer/EXT_cbtimer.o"]}, {"directory": "components/ethermind/mesh/export/climodel", "file": "components/ethermind/mesh/export/climodel/cli_model.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/climodel/cli_model.c", "-o", "build/Target 1/components/ethermind/mesh/export/climodel/cli_model.o"]}, {"directory": "components/ethermind/mesh/export/access_ps", "file": "components/ethermind/mesh/export/access_ps/access_ps.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/access_ps/access_ps.c", "-o", "build/Target 1/components/ethermind/mesh/export/access_ps/access_ps.o"]}, {"directory": "components/ethermind/mesh/export/common", "file": "components/ethermind/mesh/export/common/MS_limit_config.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/common/MS_limit_config.c", "-o", "build/Target 1/components/ethermind/mesh/export/common/MS_limit_config.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.o"]}, {"directory": "components/ethermind/mesh/export/vendormodel/client", "file": "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "-o", "build/Target 1/components/ethermind/mesh/export/vendormodel/client/vendormodel_client.o"]}, {"directory": "lib", "file": "lib/ble_host.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/ble_host.lib", "-o", "build/Target 1/lib/ble_host.o"]}, {"directory": "lib", "file": "lib/rf.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/rf.lib", "-o", "build/Target 1/lib/rf.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.c", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.h", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.h", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source", "file": "example/ble_mesh/mesh_gateway/source/main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/main.o"]}, {"directory": "components/driver/pwm", "file": "components/driver/pwm/pwm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwm/pwm.c", "-o", "build/Target 1/components/driver/pwm/pwm.o"]}, {"directory": "components/driver/flash", "file": "components/driver/flash/flash.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/flash/flash.c", "-o", "build/Target 1/components/driver/flash/flash.o"]}, {"directory": "components/driver/clock", "file": "components/driver/clock/clock.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/clock/clock.c", "-o", "build/Target 1/components/driver/clock/clock.o"]}, {"directory": "components/driver/log", "file": "components/driver/log/my_printf.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/log/my_printf.c", "-o", "build/Target 1/components/driver/log/my_printf.o"]}, {"directory": "components/driver/gpio", "file": "components/driver/gpio/gpio.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/gpio/gpio.c", "-o", "build/Target 1/components/driver/gpio/gpio.o"]}, {"directory": "components/driver/uart", "file": "components/driver/uart/uart.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/uart/uart.c", "-o", "build/Target 1/components/driver/uart/uart.o"]}, {"directory": "components/driver/pwrmgr", "file": "components/driver/pwrmgr/pwrmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwrmgr/pwrmgr.c", "-o", "build/Target 1/components/driver/pwrmgr/pwrmgr.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.o"]}, {"directory": "components/libraries/crc16", "file": "components/libraries/crc16/crc16.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/crc16/crc16.c", "-o", "build/Target 1/components/libraries/crc16/crc16.o"]}, {"directory": "components/profiles/DevInfo", "file": "components/profiles/DevInfo/devinfoservice.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/DevInfo/devinfoservice.c", "-o", "build/Target 1/components/profiles/DevInfo/devinfoservice.o"]}, {"directory": "components/profiles/GATT", "file": "components/profiles/GATT/gattservapp.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/GATT/gattservapp.c", "-o", "build/Target 1/components/profiles/GATT/gattservapp.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/peripheral.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/peripheral.c", "-o", "build/Target 1/components/profiles/Roles/peripheral.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapgattserver.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapgattserver.c", "-o", "build/Target 1/components/profiles/Roles/gapgattserver.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gap.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gap.c", "-o", "build/Target 1/components/profiles/Roles/gap.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/observer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/observer.c", "-o", "build/Target 1/components/profiles/Roles/observer.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/central.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/central.c", "-o", "build/Target 1/components/profiles/Roles/central.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapbondmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapbondmgr.c", "-o", "build/Target 1/components/profiles/Roles/gapbondmgr.o"]}, {"directory": "misc", "file": "misc/jump_table.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "misc/jump_table.c", "-o", "build/Target 1/misc/jump_table.o"]}, {"directory": "components/osal/snv", "file": "components/osal/snv/osal_snv.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/osal/snv/osal_snv.c", "-o", "build/Target 1/components/osal/snv/osal_snv.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.o"]}, {"directory": "components/driver/led_light", "file": "components/driver/led_light/led_light.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/led_light/led_light.c", "-o", "build/Target 1/components/driver/led_light/led_light.o"]}, {"directory": "components/ethermind/platforms", "file": "components/ethermind/platforms/EM_platform.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/EM_platform.c", "-o", "build/Target 1/components/ethermind/platforms/EM_platform.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes-ccm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes-ccm.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes-ccm.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_debug.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_debug.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_debug.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_os.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_os.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_os.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_timer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_timer.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_timer.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/blebrr_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/blebrr_pl.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/blebrr_pl.o"]}, {"directory": "components/ethermind/platforms/interfaces/crypto", "file": "components/ethermind/platforms/interfaces/crypto/cry.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/interfaces/crypto/cry.c", "-o", "build/Target 1/components/ethermind/platforms/interfaces/crypto/cry.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr_gatt.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/MS_common_pl.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/prov_pl.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_services.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_services.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_services.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_prov.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_prov.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_prov.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_proxy.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_proxy.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_proxy.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_clients.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_clients.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_clients.o"]}, {"directory": "components/ethermind/mesh/export/cbtimer", "file": "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "-o", "build/Target 1/components/ethermind/mesh/export/cbtimer/EXT_cbtimer.o"]}, {"directory": "components/ethermind/mesh/export/climodel", "file": "components/ethermind/mesh/export/climodel/cli_model.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/climodel/cli_model.c", "-o", "build/Target 1/components/ethermind/mesh/export/climodel/cli_model.o"]}, {"directory": "components/ethermind/mesh/export/access_ps", "file": "components/ethermind/mesh/export/access_ps/access_ps.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/access_ps/access_ps.c", "-o", "build/Target 1/components/ethermind/mesh/export/access_ps/access_ps.o"]}, {"directory": "components/ethermind/mesh/export/common", "file": "components/ethermind/mesh/export/common/MS_limit_config.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/common/MS_limit_config.c", "-o", "build/Target 1/components/ethermind/mesh/export/common/MS_limit_config.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.o"]}, {"directory": "components/ethermind/mesh/export/vendormodel/client", "file": "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "-o", "build/Target 1/components/ethermind/mesh/export/vendormodel/client/vendormodel_client.o"]}, {"directory": "lib", "file": "lib/ble_host.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/ble_host.lib", "-o", "build/Target 1/lib/ble_host.o"]}, {"directory": "lib", "file": "lib/rf.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/rf.lib", "-o", "build/Target 1/lib/rf.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.c", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.h", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.h", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source", "file": "example/ble_mesh/mesh_gateway/source/main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/main.o"]}, {"directory": "components/driver/pwm", "file": "components/driver/pwm/pwm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwm/pwm.c", "-o", "build/Target 1/components/driver/pwm/pwm.o"]}, {"directory": "components/driver/flash", "file": "components/driver/flash/flash.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/flash/flash.c", "-o", "build/Target 1/components/driver/flash/flash.o"]}, {"directory": "components/driver/clock", "file": "components/driver/clock/clock.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/clock/clock.c", "-o", "build/Target 1/components/driver/clock/clock.o"]}, {"directory": "components/driver/log", "file": "components/driver/log/my_printf.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/log/my_printf.c", "-o", "build/Target 1/components/driver/log/my_printf.o"]}, {"directory": "components/driver/gpio", "file": "components/driver/gpio/gpio.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/gpio/gpio.c", "-o", "build/Target 1/components/driver/gpio/gpio.o"]}, {"directory": "components/driver/uart", "file": "components/driver/uart/uart.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/uart/uart.c", "-o", "build/Target 1/components/driver/uart/uart.o"]}, {"directory": "components/driver/pwrmgr", "file": "components/driver/pwrmgr/pwrmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/pwrmgr/pwrmgr.c", "-o", "build/Target 1/components/driver/pwrmgr/pwrmgr.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/dongleKey.o"]}, {"directory": "components/libraries/crc16", "file": "components/libraries/crc16/crc16.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/crc16/crc16.c", "-o", "build/Target 1/components/libraries/crc16/crc16.o"]}, {"directory": "components/profiles/DevInfo", "file": "components/profiles/DevInfo/devinfoservice.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/DevInfo/devinfoservice.c", "-o", "build/Target 1/components/profiles/DevInfo/devinfoservice.o"]}, {"directory": "components/profiles/GATT", "file": "components/profiles/GATT/gattservapp.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/GATT/gattservapp.c", "-o", "build/Target 1/components/profiles/GATT/gattservapp.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/peripheral.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/peripheral.c", "-o", "build/Target 1/components/profiles/Roles/peripheral.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapgattserver.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapgattserver.c", "-o", "build/Target 1/components/profiles/Roles/gapgattserver.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gap.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gap.c", "-o", "build/Target 1/components/profiles/Roles/gap.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/observer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/observer.c", "-o", "build/Target 1/components/profiles/Roles/observer.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/central.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/central.c", "-o", "build/Target 1/components/profiles/Roles/central.o"]}, {"directory": "components/profiles/Roles", "file": "components/profiles/Roles/gapbondmgr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/profiles/Roles/gapbondmgr.c", "-o", "build/Target 1/components/profiles/Roles/gapbondmgr.o"]}, {"directory": "misc", "file": "misc/jump_table.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "misc/jump_table.c", "-o", "build/Target 1/misc/jump_table.o"]}, {"directory": "components/osal/snv", "file": "components/osal/snv/osal_snv.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/osal/snv/osal_snv.c", "-o", "build/Target 1/components/osal/snv/osal_snv.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh_Main.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.o"]}, {"directory": "components/driver/led_light", "file": "components/driver/led_light/led_light.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/driver/led_light/led_light.c", "-o", "build/Target 1/components/driver/led_light/led_light.o"]}, {"directory": "components/ethermind/platforms", "file": "components/ethermind/platforms/EM_platform.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/EM_platform.c", "-o", "build/Target 1/components/ethermind/platforms/EM_platform.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes.o"]}, {"directory": "components/ethermind/external/crypto/aes", "file": "components/ethermind/external/crypto/aes/aes-ccm.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/external/crypto/aes/aes-ccm.c", "-o", "build/Target 1/components/ethermind/external/crypto/aes/aes-ccm.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_debug.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_debug.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_debug.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_os.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_os.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_os.o"]}, {"directory": "components/ethermind/osal/src/phyos", "file": "components/ethermind/osal/src/phyos/EM_timer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/osal/src/phyos/EM_timer.c", "-o", "build/Target 1/components/ethermind/osal/src/phyos/EM_timer.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/blebrr_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/blebrr_pl.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/blebrr_pl.o"]}, {"directory": "components/ethermind/platforms/interfaces/crypto", "file": "components/ethermind/platforms/interfaces/crypto/cry.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/interfaces/crypto/cry.c", "-o", "build/Target 1/components/ethermind/platforms/interfaces/crypto/cry.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr.o"]}, {"directory": "components/ethermind/mesh/export/bearer", "file": "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/bearer/blebrr_gatt.c", "-o", "build/Target 1/components/ethermind/mesh/export/bearer/blebrr_gatt.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/MS_common_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/MS_common_pl.o"]}, {"directory": "components/ethermind/mesh/export/platforms/ext", "file": "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/platforms/ext/prov_pl.c", "-o", "build/Target 1/components/ethermind/mesh/export/platforms/ext/prov_pl.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_services.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_services.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_services.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_prov.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_prov.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_prov.o"]}, {"directory": "components/ethermind/mesh/export/appl", "file": "components/ethermind/mesh/export/appl/appl_proxy.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/appl/appl_proxy.c", "-o", "build/Target 1/components/ethermind/mesh/export/appl/appl_proxy.o"]}, {"directory": "components/ethermind/platforms/mesh", "file": "components/ethermind/platforms/mesh/mesh_clients.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/platforms/mesh/mesh_clients.c", "-o", "build/Target 1/components/ethermind/platforms/mesh/mesh_clients.o"]}, {"directory": "components/ethermind/mesh/export/cbtimer", "file": "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/cbtimer/EXT_cbtimer.c", "-o", "build/Target 1/components/ethermind/mesh/export/cbtimer/EXT_cbtimer.o"]}, {"directory": "components/ethermind/mesh/export/climodel", "file": "components/ethermind/mesh/export/climodel/cli_model.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/climodel/cli_model.c", "-o", "build/Target 1/components/ethermind/mesh/export/climodel/cli_model.o"]}, {"directory": "components/ethermind/mesh/export/access_ps", "file": "components/ethermind/mesh/export/access_ps/access_ps.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/access_ps/access_ps.c", "-o", "build/Target 1/components/ethermind/mesh/export/access_ps/access_ps.o"]}, {"directory": "components/ethermind/mesh/export/common", "file": "components/ethermind/mesh/export/common/MS_limit_config.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/common/MS_limit_config.c", "-o", "build/Target 1/components/ethermind/mesh/export/common/MS_limit_config.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_core.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_ecdh.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_mesh_models.o"]}, {"directory": "components/ethermind/lib/meshlibs/phyos/keil", "file": "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.lib", "-o", "build/Target 1/components/ethermind/lib/meshlibs/phyos/keil/libethermind_utils.o"]}, {"directory": "example/ble_mesh/mesh_gateway/source/bleMesh", "file": "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "-o", "build/Target 1/example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.o"]}, {"directory": "components/ethermind/mesh/export/vendormodel/client", "file": "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "-o", "build/Target 1/components/ethermind/mesh/export/vendormodel/client/vendormodel_client.o"]}, {"directory": "lib", "file": "lib/ble_host.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/ble_host.lib", "-o", "build/Target 1/lib/ble_host.o"]}, {"directory": "lib", "file": "lib/rf.lib", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "lib/rf.lib", "-o", "build/Target 1/lib/rf.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.c", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.c", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}, {"directory": "components/libraries/cli", "file": "components/libraries/cli/cliface.h", "arguments": ["C:/Keil_v5/ARM/ARMCC/bin/armcc.exe", "-c", "components/libraries/cli/cliface.h", "-o", "build/Target 1/components/libraries/cli/cliface.o"]}]