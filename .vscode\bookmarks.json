{"files": [{"path": "example/ble_mesh/mesh_gateway/source/bleMesh/appl_sample_mesh_gateway.c", "bookmarks": [{"line": 511, "column": 6, "label": ""}, {"line": 747, "column": 17, "label": ""}, {"line": 829, "column": 21, "label": "vendor模型注册的位置"}, {"line": 854, "column": 15, "label": ""}, {"line": 1149, "column": 0, "label": ""}, {"line": 1212, "column": 38, "label": ""}, {"line": 1414, "column": 40, "label": ""}]}, {"path": "example/ble_mesh/mesh_gateway/source/bleMesh/OSAL_bleMesh.c", "bookmarks": [{"line": 87, "column": 9, "label": ""}]}, {"path": "example/ble_mesh/mesh_gateway/source/bleMesh/bleMesh.c", "bookmarks": [{"line": 194, "column": 8, "label": ""}, {"line": 529, "column": 38, "label": "按键触发位置，用AT事件触发这个事件"}, {"line": 590, "column": 68, "label": ""}, {"line": 690, "column": 2, "label": ""}]}, {"path": "components/ethermind/mesh/export/climodel/cli_model.c", "bookmarks": [{"line": 82, "column": 30, "label": ""}]}, {"path": "components/ethermind/mesh/export/include/MS_generic_onoff_api.h", "bookmarks": [{"line": 258, "column": 10, "label": ""}]}, {"path": "components/ethermind/mesh/export/include/MS_access_api.h", "bookmarks": [{"line": 564, "column": 10, "label": ""}, {"line": 1801, "column": 11, "label": ""}]}, {"path": "components/ethermind/mesh/export/vendormodel/client/vendormodel_client.c", "bookmarks": [{"line": 205, "column": 44, "label": ""}]}, {"path": "components/ethermind/mesh/export/appl/model/client/appl_config_client.c", "bookmarks": [{"line": 97, "column": 26, "label": "关键，代码里面设置参数"}]}]}