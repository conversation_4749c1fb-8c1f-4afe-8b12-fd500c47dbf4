; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x1fff1838 0xE7C8 {  ; load region size_region
 ER_IROM1 0x1fff1838 0xE7C8  {  ; load address = execution address
  *.o (RESET, +First)
  *(InRoot$$Sections)
  *.o(_section_sram_code_)
  .ANY (+RO)  
  .ANY (+RW +ZI)
 }
}
LR_ROM_JT_GC  0x1fff0000 0x00800 {
  JUMP_TABLE 0x1fff0000 0x00400  {
   .ANY (jump_table_mem_area) 
	
  }
  GOLBAL_CONFIG 0x1fff0400 0x00400  {
   .ANY (global_config_area) 
	
  }  
 } 
LR_ROM_XIP  0x11020000 0x01e000 {
  ER_ROM_XIP 0x11020000 0x01e000  {  ; load address = execution address
	libethermind_mesh_models.lib (+RO) 
	libethermind_utils.lib (+RO) 
	libethermind_mesh_core.lib (+RO)
	appl_sample_mesh_gateway.o(+RO)
	devinfoservice.o(+RO)
	gatt*.o(+RO)
	gattservapp.o(+RO)
	l2cap*.o(+RO)
	att*.o(+RO)
	linkdb.o(+RO)
	sm*.o(+RO)
	gap*.o(+RO)
	peripheral.o(+RO)
	led_light.o(+RO)
	pwm.o(+RO)
	uart.o(+RO)
	em_timer.o(+RO)
	vendormodel_client.o(+RO)
	;mesh_clients.o(+RO)
	;model_state_handler_pl.o(+RO)
	blebrr_pl.o(+RO)
	blebrr.o(+RO)
	blebrr_gatt.o(+RO)
	blemesh_main.o(+RO)
	;appl_prov.o(+RO)
	blemesh.o(+RO)
	my_printf.o(+RO)
	osal_snv.o(+RO)
	gapgattserver.o(+RO)
	gpio.o(+RO)
	mesh_services.o(+RO)
	;ota_app_service.o(+RO)
	crc16.o(+RO)
	clock.o(+RO)
	em_debug.o(+RO)
	em_os.o(+RO)
	cry.o(+RO)
	aes-ccm.o(+RO)
	aes.o(+RO)
	cli_model.o(+RO)
	access_ps.o(+RO)
	ms_limit_config.o(+RO)
	printfa.o(+RO)
;	appl_prov.o(+RO)
   *.o(_section_xip_code_, _func_xip_code_.*)
  }
 }  


